# Python 3.9 Migration Guide for CoHost.AI

This guide will help you migrate from Python 3.13 to Python 3.9 to enable xTTS v2 compatibility.

## Why Python 3.9?

- **xTTS v2 Requirement**: <PERSON><PERSON> (xTTS v2) requires Python >= 3.9 and < 3.12
- **Compatibility**: All existing dependencies and code features work with Python 3.9
- **Future-proof**: Allows integration of advanced TTS capabilities

## Migration Steps

### Step 1: Install Python 3.9

#### Option A: Using pyenv (Recommended)
```bash
# Install pyenv if not already installed
# Windows (using Git Bash or WSL)
curl https://pyenv.run | bash

# Install Python 3.9.18 (latest 3.9.x)
pyenv install 3.9.18
pyenv global 3.9.18
```

#### Option B: Direct Installation
1. Download Python 3.9.18 from [python.org](https://www.python.org/downloads/release/python-3918/)
2. Install following the installer instructions
3. Ensure Python 3.9 is in your PATH

### Step 2: Create New Virtual Environment

```bash
# Navigate to your project directory
cd "C:\Users\<USER>\Desktop\CoHost.AI"

# Remove old virtual environment
rmdir /s venv

# Create new virtual environment with Python 3.9
python3.9 -m venv venv

# Activate the virtual environment
# Windows
venv\Scripts\activate

# Verify Python version
python --version
# Should show: Python 3.9.18 (or similar 3.9.x)
```

### Step 3: Install Dependencies

```bash
# Upgrade pip
python -m pip install --upgrade pip

# Install existing dependencies
pip install -r requirements.txt

# Test the installation
python -c "import rich; print('Rich version:', rich.__version__)"
```

### Step 4: Test xTTS v2 Installation

```bash
# Uncomment the TTS line in requirements.txt first
# Then install TTS
pip install TTS

# Test xTTS v2
python -c "from TTS.api import TTS; print('xTTS v2 available!')"
```

### Step 5: Update Environment Configuration

Update your `.env` file if needed:
```bash
# Add TTS configuration (example)
# TTS_MODEL=tts_models/multilingual/multi-dataset/xtts_v2
# TTS_DEVICE=cpu  # or 'cuda' if you have GPU
```

## Verification Checklist

- [ ] Python version is 3.9.x: `python --version`
- [ ] Virtual environment is activated
- [ ] All existing dependencies install successfully
- [ ] Rich library works: `python -c "import rich; rich.print('✅ Rich works!')"`
- [ ] Google Cloud TTS works: `python -c "from google.cloud import texttospeech; print('✅ Google TTS works!')"`
- [ ] xTTS v2 installs: `pip install TTS`
- [ ] xTTS v2 imports: `python -c "from TTS.api import TTS; print('✅ xTTS v2 works!')"`
- [ ] CoHost.AI runs: `python run.py`

## Troubleshooting

### Common Issues

1. **"TTS requires python >= 3.9 and < 3.12"**
   - Verify Python version: `python --version`
   - Ensure you're in the correct virtual environment

2. **PyAudio installation fails**
   - Windows: `pip install pipwin && pipwin install pyaudio`
   - Or download wheel from [here](https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio)

3. **CUDA/GPU issues with TTS**
   - Install CPU version first: `pip install TTS`
   - For GPU: Ensure CUDA toolkit is compatible

### Rollback Plan

If you need to rollback:
```bash
# Deactivate current environment
deactivate

# Remove Python 3.9 environment
rmdir /s venv

# Recreate with Python 3.13
python3.13 -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

## Next Steps After Migration

1. **Test xTTS v2 Integration**: Create a simple TTS test script
2. **Update Documentation**: Reflect new Python requirement in project docs
3. **Consider TTS Manager Updates**: Modify `tts_manager.py` to support both Google Cloud TTS and xTTS v2
4. **Performance Testing**: Compare TTS performance between Google Cloud and xTTS v2

## Support

If you encounter issues:
1. Check the [Coqui TTS GitHub](https://github.com/coqui-ai/TTS) for known issues
2. Verify all dependencies are compatible with Python 3.9
3. Test each component individually before running the full application

---

**Note**: This migration maintains backward compatibility while enabling xTTS v2 integration. All existing functionality will continue to work as before.
